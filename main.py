#!/usr/bin/env python3
"""Productivity Guard - AI-driven focus companion.
There are multiple implementations that can be run out of this main.py"""

import os
import sys
from pathlib import Path
from dotenv import load_dotenv

from src.config import cfg

# Implementation picker - choose which screenshot implementation to use
IMPLEMENTATION_PICKER = "o3-pro-pure"  # Options: "integrated", "screenshot_taker", "legacy"

def get_cli_app():
    """Get the appropriate CLI app based on implementation picker."""
    if IMPLEMENTATION_PICKER == "o3-pro-pure":
        # Use the fully integrated implementation
        from src.cli_o3_pro_pure import app
        return app
    else:
        raise ValueError(f"Unknown implementation: {IMPLEMENTATION_PICKER}")

def main():
    """Main entry point for Productivity Guard."""
    # Initialize logging
    cfg.log_init()

    # Ensure output directory exists
    Path(cfg.OUTPUT_DIR).mkdir(parents=True, exist_ok=True)

    # Load .env
    env_file = Path(".env")
    load_dotenv()

    # Get and run the appropriate CLI application
    try:
        app = get_cli_app()
        app()
    except ValueError as e:
        print(f"Error: {e}")
        print("Available implementations: 'o3-pro-pure'....")
        sys.exit(1)


if __name__ == "__main__":
    main()
